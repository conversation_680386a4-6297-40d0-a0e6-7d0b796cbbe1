'use client'
import Sidebar from './Sidebar'
import { createContext, useCallback, useContext, useEffect, useRef, useState } from 'react'

import useSWR from 'swr'
// import Avatar from '@/app/components/base/avatar'
import decorate from '@/assets/images/layout/decorate.png'
import CustomDialog from '@/app/components/base/dialog'
import LoginPage from '@/app/home/<USER>/page'
import { fetchUserProfile } from '@/service/common'
import type { UserProfileResponse } from '@/models/common'

// 声明window扩展属性用于全局登录弹窗
declare global {
  interface Window {
    showHomeLoginDialog?: () => void
  }
}

// 创建 HomeContext
type HomeContextType = {
  isLogin: boolean
  showLoginDialog: () => void
  reloadData: () => void
  userProfile: UserProfileResponse | null
}

const HomeContext = createContext<HomeContextType | null>(null)

export const useHomeContext = () => {
  const context = useContext(HomeContext)
  if (!context)
    throw new Error('无用户信息')
  return context
}

function LayoutContent({ children }: { children: React.ReactNode }) {
  // 使用fetchUserProfile接口获取用户信息
  const { data: userProfileResponse, mutate: mutateUserProfile, error: profileError, isLoading } = useSWR(
    { url: '/account/profile', params: {} },
    fetchUserProfile,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      shouldRetryOnError: false,
      dedupingInterval: 5000,
      // 立即开始请求，不等待
      suspense: false,
    },
  )

  // 防抖机制：记录上次弹出登录对话框的时间
  const lastLoginDialogTime = useRef(0)

  // 处理用户信息
  const [userProfile, setUserProfile] = useState<UserProfileResponse | null>(null)
  const [isLogin, setIsLogin] = useState(false)
  const [selectedMenu, setSelectedMenu] = useState(localStorage.getItem('selectedMenu') || 'HomePage')
  const [showLoginDialog, setShowLoginDialog] = useState(false)
  const [hasInitialized, setHasInitialized] = useState(false)

  // 保存用户信息
  useEffect(() => {
    const processUserProfile = async () => {
      // 如果正在加载，等待
      if (isLoading)
        return

      // 标记已初始化
      if (!hasInitialized)
        setHasInitialized(true)

      // 如果有错误（比如401未登录），设置为未登录状态
      if (profileError) {
        console.log('err', profileError)
        setUserProfile(null)
        setIsLogin(false)
        return
      }

      // 如果有响应数据，处理用户信息
      if (userProfileResponse) {
        try {
          const profile: UserProfileResponse = await userProfileResponse.json()
          console.log('用户信息', profile)
          setUserProfile(profile)
          setIsLogin(!!profile?.id)
        }
        catch (error) {
          console.warn('err', error)
          setUserProfile(null)
          setIsLogin(false)
        }
      }
      else if (hasInitialized) {
        console.log('hasInitialized', hasInitialized)
        // 如果已经初始化但没有数据，说明未登录
        setUserProfile(null)
        setIsLogin(false)
      }
    }

    processUserProfile()
  }, [userProfileResponse, profileError, isLoading, hasInitialized])

  const showLoginDialogHandler = useCallback(() => {
    const now = Date.now()
    // 防止重复弹出登录对话框：如果已经显示或者距离上次弹出不到3秒，则不再弹出
    if (!showLoginDialog && (now - lastLoginDialogTime.current > 3000)) {
      lastLoginDialogTime.current = now
      setShowLoginDialog(true)
    }
  }, [showLoginDialog])

  // 数据重新加载方法
  const reloadData = async () => {
    // 重新获取用户信息
    await mutateUserProfile()
    // 触发全局数据重新加载事件
    window.dispatchEvent(new CustomEvent('homeDataReload'))
  }

  // 全局挂载弹窗触发方法
  useEffect(() => {
    window.showHomeLoginDialog = showLoginDialogHandler
    return () => {
      delete window.showHomeLoginDialog
    }
  }, [showLoginDialogHandler])

  const homeContextValue = {
    isLogin,
    showLoginDialog: showLoginDialogHandler,
    reloadData,
    userProfile,
  }

  return (
    <HomeContext.Provider value={homeContextValue}>
      <div className="flex min-h-screen w-full overflow-x-auto bg-[#F3F4F6] font-[PingFangSC]">
        {/* 侧边栏 */}
        <div className="shrink-0">
          <Sidebar onSelect={setSelectedMenu} selectedKey={selectedMenu} />
        </div>
        {/* 主体内容 */}
        <main className="relative flex h-screen min-w-0 flex-1 flex-col items-center overflow-auto rounded-[11px] border-[1px] border-[#FFFFFF]" style={{ background: 'linear-gradient(180deg, #ECF4FF 0%, #FFFFFF 30%, #FFFFFF 100%)' }}>
          <img src={decorate.src} alt="decorateIcon" className='absolute right-[285px] top-[35px] hidden lg:block' />
          {children}
          {/* 全局登录弹窗 */}
          <CustomDialog
            show={showLoginDialog}
            onClose={() => setShowLoginDialog(false)}
            className="relative p-0"
            maskClassName="!bg-black !bg-opacity-60"
          >
            <LoginPage
              onSuccess={async () => {
                // 登录成功后重新加载数据
                await reloadData()
                setShowLoginDialog(false)
              }}
            />
            <button
              className="absolute right-[20px] top-[15px] size-[20px] text-[20px] text-[#212121]"
              onClick={() => setShowLoginDialog(false)}
            >
              ×
            </button>
          </CustomDialog>
        </main>
      </div>
    </HomeContext.Provider>
  )
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <LayoutContent>{children}</LayoutContent>
  )
}
