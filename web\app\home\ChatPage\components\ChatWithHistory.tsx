'use client'
import type { FC } from 'react'
import {
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react'
import { useAsyncEffect } from 'ahooks'
import { useThemeContext } from '@/app/components/base/chat/embedded-chatbot/theme/theme-context'
import {
  ChatWithHistoryContext,
  useChatWithHistoryContext,
} from '@/app/components/base/chat/chat-with-history/context'
import { useChatWithHistory } from '@/app/components/base/chat/chat-with-history/hooks'
import Sidebar from '@/app/home/<USER>/components/Sidebar'
// import Header from '@/app/components/base/chat/chat-with-history/header'
import ChatWrapper from '@/app/home/<USER>/components/ChatWrapper'
import type { InstalledApp } from '@/models/explore'
import Loading from '@/app/components/base/loading'
import useBreakpoints, { MediaType } from '@/hooks/use-breakpoints'
import { checkOrSetAccessToken, removeAccessToken } from '@/app/components/share/utils'
import AppUnavailable from '@/app/components/base/app-unavailable'
import cn from '@/utils/classnames'
import useDocumentTitle from '@/hooks/use-document-title'
import { useTranslation } from 'react-i18next'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { useDataReload } from '@/app/home/<USER>/useDataReload'
import useSWR from 'swr'
import { fetchAppParams, fetchAppMeta, fetchConversations, fetchChatList } from '@/service/share'

type ChatWithHistoryProps = {
  className?: string
}
const ChatWithHistory: FC<ChatWithHistoryProps> = ({
  className,
}) => {
  const {
    userCanAccess,
    appInfoError,
    appData,
    appInfoLoading,
    appChatListDataLoading,
    chatShouldReloadKey,
    isMobile,
    themeBuilder,
    sidebarCollapseState,
    isInstalledApp,
    handleChangeConversation,
    currentConversationId,
    handleNewConversation, // 新建对话
    conversationList, // 新增
  } = useChatWithHistoryContext()
  const isSidebarCollapsed = sidebarCollapseState
  const customConfig = appData?.custom_config
  const site = appData?.site

  const [showSidePanel, setShowSidePanel] = useState(false)

  useEffect(() => {
    themeBuilder?.buildTheme(site?.chat_color_theme, site?.chat_color_theme_inverted)
  }, [site, customConfig, themeBuilder])

  useDocumentTitle(site?.title || 'Chat')

  const { t } = useTranslation()
  const searchParams = useSearchParams()
  const conversationId = searchParams.get('conversationId')
  const question = searchParams.get('question')
  const hasSentRef = useRef(false)

  // 监听 URL 参数并切换会话
  useEffect(() => {
    if (conversationId && conversationId !== currentConversationId)
      handleChangeConversation(conversationId)
  }, [conversationId, currentConversationId, handleChangeConversation])

  useEffect(() => {
    if (question && !hasSentRef.current) {
      // 新建对话并发送问题
      handleNewConversation()
      setTimeout(() => {
        // 找到 ChatWrapper 的 doSend 或 handleSend 方法
        // 通过 window 事件通知 ChatWrapper 发送
        window.dispatchEvent(new CustomEvent('send-question', { detail: { question } }))
      }, 300)
      hasSentRef.current = true
      // 可选：跳转去掉 question 参数，避免刷新重复
      const params = new URLSearchParams(searchParams)
      params.delete('question')
      window.history.replaceState(null, '', `${window.location.pathname}?${params.toString()}`)
    }
  }, [question, handleNewConversation, searchParams])

  const router = useRouter()
  const pathname = usePathname()
  const getSigninUrl = useCallback(() => {
    const params = new URLSearchParams(searchParams)
    params.delete('message')
    params.set('redirect_url', pathname)
    return `/webapp-signin?${params.toString()}`
  }, [searchParams, pathname])

  const backToHome = useCallback(() => {
    removeAccessToken()
    const url = getSigninUrl()
    router.replace(url)
  }, [getSigninUrl, router])

  const prevConversationListLength = useRef(conversationList.length)
  useEffect(() => {
    if (conversationList.length > prevConversationListLength.current) {
      // 新对话创建后，conversationList 增加，自动切换到最新的 id
      const latest = conversationList[0]
      if (latest && latest.id && latest.id !== currentConversationId)
        handleChangeConversation(latest.id)
    }
    prevConversationListLength.current = conversationList.length
  }, [conversationList, currentConversationId, handleChangeConversation])

  if (appInfoLoading) {
    return (
      <Loading type='app' />
    )
  }
  if (!userCanAccess) {
    return <div className='flex h-full flex-col items-center justify-center gap-y-2'>
      <AppUnavailable className='h-auto w-auto' code={403} unknownReason='no permission.' />
      {!isInstalledApp && <span className='system-sm-regular cursor-pointer text-text-tertiary' onClick={backToHome}>{t('common.userProfile.logout')}</span>}
    </div>
  }

  if (appInfoError) {
    return (
      <AppUnavailable />
    )
  }

  return (
    <div className={cn(
      'flex h-full bg-background-default-burn',
      isMobile && 'flex-col',
      className,
    )}>
      {/* {!isMobile && (
        <div className={cn(
          'flex w-[236px] flex-col p-1 pr-0 transition-all duration-200 ease-in-out',
          isSidebarCollapsed && 'w-0 overflow-hidden !p-0',
        )}>
          <Sidebar />
        </div>
      )}
      {isMobile && (
        <HeaderInMobile />
      )} */}
      <div className={cn('relative grow p-2', isMobile && 'h-[calc(100%_-_56px)] p-0')}>
        {isSidebarCollapsed && (
          <div
            className={cn(
              'absolute top-0 z-20 flex h-full w-[256px] flex-col p-2 transition-all duration-500 ease-in-out',
              showSidePanel ? 'left-0' : 'left-[-248px]',
            )}
            onMouseEnter={() => setShowSidePanel(true)}
            onMouseLeave={() => setShowSidePanel(false)}
          >
            <Sidebar isPanel />
          </div>
        )}
        <div className={cn('flex h-full flex-col overflow-hidden border-[0,5px] border-components-panel-border-subtle bg-chatbot-bg', isMobile ? 'rounded-t-2xl' : 'rounded-2xl')}>
          {/* {!isMobile && <Header />} */}
          {appChatListDataLoading && (
            <Loading type='app' />
          )}
          {!appChatListDataLoading && (
            <ChatWrapper key={chatShouldReloadKey} />
          )}
        </div>
      </div>
    </div>
  )
}

export type ChatWithHistoryWrapProps = {
  installedAppInfo?: InstalledApp
  className?: string
}
const ChatWithHistoryWrap: FC<ChatWithHistoryWrapProps> = ({
  installedAppInfo,
  className,
}) => {
  const media = useBreakpoints()
  const isMobile = media === MediaType.mobile
  const themeBuilder = useThemeContext()

  const {
    appInfoError,
    appInfoLoading,
    userCanAccess,
    appData,
    appParams,
    appMeta,
    appChatListDataLoading,
    currentConversationId,
    currentConversationItem,
    appPrevChatTree,
    pinnedConversationList,
    conversationList,
    newConversationInputs,
    newConversationInputsRef,
    handleNewConversationInputsChange,
    inputsForms,
    handleNewConversation,
    handleStartChat,
    handleChangeConversation,
    handlePinConversation,
    handleUnpinConversation,
    handleDeleteConversation,
    conversationRenaming,
    handleRenameConversation,
    handleNewConversationCompleted,
    chatShouldReloadKey,
    isInstalledApp,
    appId,
    handleFeedback,
    currentChatInstanceRef,
    sidebarCollapseState,
    handleSidebarCollapse,
    clearChatList,
    setClearChatList,
    isResponding,
    setIsResponding,
    currentConversationInputs,
    setCurrentConversationInputs,
    allInputsHidden,
  } = useChatWithHistory(installedAppInfo)

  // 在home页面中监听SWR错误，用于触发登录对话框
  const { error: appParamsError, mutate: mutateAppParams } = useSWR(['appParams', isInstalledApp, appId], () => fetchAppParams(isInstalledApp, appId), { revalidateOnFocus: false })
  const { error: appMetaError, mutate: mutateAppMeta } = useSWR(['appMeta', isInstalledApp, appId], () => fetchAppMeta(isInstalledApp, appId), { revalidateOnFocus: false })
  const { error: appConversationError, mutate: mutateAppConversation } = useSWR(['appConversationData', isInstalledApp, appId, false], () => fetchConversations(isInstalledApp, appId, undefined, false, 100), { revalidateOnFocus: false })
  const { error: appChatListError, mutate: mutateAppChatList } = useSWR(chatShouldReloadKey ? ['appChatList', chatShouldReloadKey, isInstalledApp, appId] : null, () => fetchChatList(chatShouldReloadKey, isInstalledApp, appId), { revalidateOnFocus: false })

  // 处理SWR错误，检查是否需要触发登录对话框
  useEffect(() => {
    const errors = [appParamsError, appMetaError, appConversationError, appChatListError]
    const homePageUnAuthError = errors.find(error => error?.isHomePageUnauth)

    if (homePageUnAuthError && window.showHomeLoginDialog) {
      window.showHomeLoginDialog()
    }
  }, [appParamsError, appMetaError, appConversationError, appChatListError])

  // 监听登录成功后的数据重新加载事件
  useDataReload(() => {
    // 重新获取所有数据
    mutateAppParams()
    mutateAppMeta()
    mutateAppConversation()
    if (chatShouldReloadKey) {
      mutateAppChatList()
    }
  })

  return (
    <ChatWithHistoryContext.Provider value={{
      appInfoError,
      appInfoLoading,
      appData,
      userCanAccess,
      appParams,
      appMeta,
      appChatListDataLoading,
      currentConversationId,
      currentConversationItem,
      appPrevChatTree,
      pinnedConversationList,
      conversationList,
      newConversationInputs,
      newConversationInputsRef,
      handleNewConversationInputsChange,
      inputsForms,
      handleNewConversation,
      handleStartChat,
      handleChangeConversation,
      handlePinConversation,
      handleUnpinConversation,
      handleDeleteConversation,
      conversationRenaming,
      handleRenameConversation,
      handleNewConversationCompleted,
      chatShouldReloadKey,
      isMobile,
      isInstalledApp,
      appId,
      handleFeedback,
      currentChatInstanceRef,
      themeBuilder,
      sidebarCollapseState,
      handleSidebarCollapse,
      clearChatList,
      setClearChatList,
      isResponding,
      setIsResponding,
      currentConversationInputs,
      setCurrentConversationInputs,
      allInputsHidden,
    }}>
      <ChatWithHistory className={className} />
    </ChatWithHistoryContext.Provider>
  )
}

const ChatWithHistoryWrapWithCheckToken: FC<ChatWithHistoryWrapProps> = ({
  installedAppInfo,
  className,
}) => {
  const [initialized, setInitialized] = useState(false)
  const [appUnavailable, setAppUnavailable] = useState<boolean>(false)
  const [isUnknownReason, setIsUnknownReason] = useState<boolean>(false)

  useAsyncEffect(async () => {
    if (!initialized) {
      if (!installedAppInfo) {
        try {
          await checkOrSetAccessToken()
        }
        catch (e: any) {
          if (e.status === 404) {
            setAppUnavailable(true)
          }
          else {
            setIsUnknownReason(true)
            setAppUnavailable(true)
          }
        }
      }
      setInitialized(true)
    }
  }, [])

  if (!initialized)
    return null

  if (appUnavailable)
    return <AppUnavailable isUnknownReason={isUnknownReason} />

  return (
    <ChatWithHistoryWrap
      installedAppInfo={installedAppInfo}
      className={className}
    />
  )
}

export default ChatWithHistoryWrapWithCheckToken
